import animate from 'tailwindcss-animate'

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Kadzia brand colors
        primary: {
          DEFAULT: '#2e3192',
          50: '#f0f1ff',
          100: '#e4e6ff',
          200: '#cdd0ff',
          300: '#a6abff',
          400: '#7a7dff',
          500: '#5a5eff',
          600: '#4a3ff5',
          700: '#3f32d8',
          800: '#342bb0',
          900: '#2e3192',
          950: '#1a1a54',
        },
        accent: {
          DEFAULT: '#1bffff',
          50: '#f0fffe',
          100: '#ccfffe',
          200: '#99ffff',
          300: '#5cffff',
          400: '#1bffff',
          500: '#00e6e6',
          600: '#00b8b8',
          700: '#009494',
          800: '#007575',
          900: '#006161',
          950: '#003a3a',
        },
        // Semantic colors
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      animation: {
        'gradient-shift': 'gradientShift 8s ease infinite',
      },
      keyframes: {
        gradientShift: {
          '0%': { 'background-position': '0% 50%' },
          '50%': { 'background-position': '100% 50%' },
          '100%': { 'background-position': '0% 50%' },
        },
      },
      backgroundImage: {
        'kadzia-gradient': 'linear-gradient(135deg, #2e3192 0%, #1bffff 100%)',
        'kadzia-gradient-hover': 'linear-gradient(135deg, #252a7a 0%, #17e6e6 100%)',
      },
    },
  },
  plugins: [animate],
}
