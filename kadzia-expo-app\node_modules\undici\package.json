{"name": "undici", "version": "6.21.3", "description": "An HTTP/1.1 client, written from scratch for Node.js", "homepage": "https://undici.nodejs.org", "bugs": {"url": "https://github.com/nodejs/undici/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nodejs/undici.git"}, "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/dnlup", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ethan-arrowood", "author": true}, {"name": "<PERSON>", "url": "https://github.com/mcollina", "author": true}, {"name": "<PERSON>", "url": "https://github.com/KhafraDev", "author": true}, {"name": "<PERSON>", "url": "https://github.com/ronag", "author": true}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/szmarczak", "author": true}, {"name": "<PERSON>", "url": "https://github.com/delvedor", "author": true}], "keywords": ["fetch", "http", "https", "promise", "request", "curl", "wget", "xhr", "whatwg"], "main": "index.js", "types": "index.d.ts", "scripts": {"build:node": "npx esbuild@0.19.10 index-fetch.js --bundle --platform=node --outfile=undici-fetch.js --define:esbuildDetection=1 --keep-names && node scripts/strip-comments.js", "prebuild:wasm": "node build/wasm.js --prebuild", "build:wasm": "node build/wasm.js --docker", "lint": "standard | snazzy", "lint:fix": "standard --fix | snazzy", "test": "npm run test:javascript && cross-env NODE_V8_COVERAGE=  npm run test:typescript", "test:javascript": "node scripts/generate-pem && npm run test:unit && npm run test:node-fetch && npm run test:cache && npm run test:interceptors && npm run test:fetch && npm run test:cookies && npm run test:eventsource && npm run test:wpt && npm run test:websocket && npm run test:node-test && npm run test:jest", "test:javascript:withoutintl": "node scripts/generate-pem && npm run test:unit && npm run test:node-fetch && npm run test:fetch:nobuild && npm run test:cache && npm run test:interceptors && npm run test:cookies && npm run test:eventsource:nobuild && npm run test:wpt:withoutintl && npm run test:node-test", "test:busboy": "borp -p \"test/busboy/*.js\"", "test:cache": "borp -p \"test/cache/*.js\"", "test:cookies": "borp -p \"test/cookie/*.js\"", "test:eventsource": "npm run build:node && npm run test:eventsource:nobuild", "test:eventsource:nobuild": "borp --expose-gc -p \"test/eventsource/*.js\"", "test:fuzzing": "node test/fuzzing/fuzzing.test.js", "test:fetch": "npm run build:node && npm run test:fetch:nobuild", "test:fetch:nobuild": "borp --timeout 180000 --expose-gc --concurrency 1 -p \"test/fetch/*.js\" && npm run test:webidl && npm run test:busboy", "test:h2": "npm run test:h2:core && npm run test:h2:fetch", "test:h2:core": "borp -p \"test/http2*.js\"", "test:h2:fetch": "npm run build:node && borp -p \"test/fetch/http2*.js\"", "test:interceptors": "borp -p \"test/interceptors/*.js\"", "test:jest": "cross-env NODE_V8_COVERAGE= jest", "test:unit": "borp --expose-gc -p \"test/*.js\"", "test:node-fetch": "borp -p \"test/node-fetch/**/*.js\"", "test:node-test": "borp -p \"test/node-test/**/*.js\"", "test:tdd": "borp --expose-gc -p \"test/*.js\"", "test:tdd:node-test": "borp -p \"test/node-test/**/*.js\" -w", "test:typescript": "tsd && tsc test/imports/undici-import.ts --typeRoots ./types && tsc ./types/*.d.ts --noEmit --typeRoots ./types", "test:webidl": "borp -p \"test/webidl/*.js\"", "test:websocket": "borp -p \"test/websocket/*.js\"", "test:websocket:autobahn": "node test/autobahn/client.js", "test:websocket:autobahn:report": "node test/autobahn/report.js", "test:wpt": "node test/wpt/start-fetch.mjs && node test/wpt/start-FileAPI.mjs && node test/wpt/start-mimesniff.mjs && node test/wpt/start-xhr.mjs && node test/wpt/start-websockets.mjs && node test/wpt/start-cacheStorage.mjs && node test/wpt/start-eventsource.mjs", "test:wpt:withoutintl": "node test/wpt/start-fetch.mjs && node test/wpt/start-mimesniff.mjs && node test/wpt/start-xhr.mjs && node test/wpt/start-cacheStorage.mjs && node test/wpt/start-eventsource.mjs", "coverage": "npm run coverage:clean && cross-env NODE_V8_COVERAGE=./coverage/tmp npm run test:javascript && npm run coverage:report", "coverage:ci": "npm run coverage:clean && cross-env NODE_V8_COVERAGE=./coverage/tmp npm run test:javascript && npm run coverage:report:ci", "coverage:clean": "node ./scripts/clean-coverage.js", "coverage:report": "cross-env NODE_V8_COVERAGE= c8 report", "coverage:report:ci": "c8 report", "bench": "echo \"Error: Benchmarks have been moved to '/benchmarks'\" && exit 1", "serve:website": "echo \"Error: Documentation has been moved to '/docs'\" && exit 1", "prepare": "husky && node ./scripts/platform-shell.js"}, "devDependencies": {"@fastify/busboy": "2.1.1", "@matteo.collina/tspl": "^0.1.1", "@sinonjs/fake-timers": "^11.1.0", "@types/node": "~18.19.50", "abort-controller": "^3.0.0", "borp": "^0.15.0", "c8": "^10.0.0", "cross-env": "^7.0.3", "dns-packet": "^5.4.0", "fast-check": "^3.17.1", "form-data": "^4.0.0", "formdata-node": "^6.0.3", "https-pem": "^3.0.0", "husky": "^9.0.7", "jest": "^29.0.2", "jsdom": "^24.0.0", "node-forge": "^1.3.1", "pre-commit": "^1.2.2", "proxy": "^2.1.1", "snazzy": "^9.0.0", "standard": "^17.0.0", "tsd": "^0.31.0", "typescript": "^5.0.2", "ws": "^8.11.0"}, "engines": {"node": ">=18.17"}, "standard": {"env": ["jest"], "ignore": ["lib/llhttp/constants.js", "lib/llhttp/utils.js", "test/fixtures/wpt"]}, "tsd": {"directory": "test/types", "compilerOptions": {"esModuleInterop": true, "lib": ["esnext"]}}, "jest": {"testMatch": ["<rootDir>/test/jest/**"]}}