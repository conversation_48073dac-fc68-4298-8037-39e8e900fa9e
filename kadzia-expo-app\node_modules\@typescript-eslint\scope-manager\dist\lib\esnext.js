"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext = void 0;
const es2024_1 = require("./es2024");
const esnext_array_1 = require("./esnext.array");
const esnext_collection_1 = require("./esnext.collection");
const esnext_decorators_1 = require("./esnext.decorators");
const esnext_disposable_1 = require("./esnext.disposable");
const esnext_float16_1 = require("./esnext.float16");
const esnext_intl_1 = require("./esnext.intl");
const esnext_iterator_1 = require("./esnext.iterator");
const esnext_promise_1 = require("./esnext.promise");
exports.esnext = {
    libs: [
        es2024_1.es2024,
        esnext_intl_1.esnext_intl,
        esnext_decorators_1.esnext_decorators,
        esnext_disposable_1.esnext_disposable,
        esnext_collection_1.esnext_collection,
        esnext_array_1.esnext_array,
        esnext_iterator_1.esnext_iterator,
        esnext_promise_1.esnext_promise,
        esnext_float16_1.esnext_float16,
    ],
    variables: [],
};
