"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.customDirectEventTypes = void 0;
// customDirectEventTypes doesn't exist in react-native-web, therefore importing it
// directly in createHandler.tsx would end in crash.
const customDirectEventTypes = {};
exports.customDirectEventTypes = customDirectEventTypes;
//# sourceMappingURL=customDirectEventTypes.web.js.map