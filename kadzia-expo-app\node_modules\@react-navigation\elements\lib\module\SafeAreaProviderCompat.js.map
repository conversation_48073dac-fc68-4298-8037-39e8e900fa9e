{"version": 3, "names": ["React", "Dimensions", "Platform", "StyleSheet", "View", "initialWindowMetrics", "SafeAreaInsetsContext", "SafeAreaProvider", "FrameSizeProvider", "jsx", "_jsx", "width", "height", "get", "initialMetrics", "OS", "frame", "x", "y", "insets", "top", "left", "right", "bottom", "SafeAreaProviderCompat", "children", "style", "useContext", "initialFrame", "styles", "container", "create", "flex"], "sourceRoot": "../../src", "sources": ["SafeAreaProviderCompat.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,UAAU,EACVC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AACrB,SACEC,oBAAoB,EACpBC,qBAAqB,EACrBC,gBAAgB,QACX,gCAAgC;AAEvC,SAASC,iBAAiB,QAAQ,mBAAgB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAOnD,MAAM;EAAEC,KAAK,GAAG,CAAC;EAAEC,MAAM,GAAG;AAAE,CAAC,GAAGX,UAAU,CAACY,GAAG,CAAC,QAAQ,CAAC;;AAE1D;AACA;AACA;AACA,MAAMC,cAAc,GAClBZ,QAAQ,CAACa,EAAE,KAAK,KAAK,IAAIV,oBAAoB,IAAI,IAAI,GACjD;EACEW,KAAK,EAAE;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEP,KAAK;IAAEC;EAAO,CAAC;EACpCO,MAAM,EAAE;IAAEC,GAAG,EAAE,CAAC;IAAEC,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE;AACjD,CAAC,GACDlB,oBAAoB;AAE1B,OAAO,SAASmB,sBAAsBA,CAAC;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EACjE,MAAMP,MAAM,GAAGnB,KAAK,CAAC2B,UAAU,CAACrB,qBAAqB,CAAC;EAEtDmB,QAAQ,gBACNf,IAAA,CAACF,iBAAiB;IAACoB,YAAY,EAAEd,cAAc,CAACE,KAAM;IAAAS,QAAA,EACnDA;EAAQ,CACQ,CACpB;EAED,IAAIN,MAAM,EAAE;IACV;IACA;IACA;IACA,oBAAOT,IAAA,CAACN,IAAI;MAACsB,KAAK,EAAE,CAACG,MAAM,CAACC,SAAS,EAAEJ,KAAK,CAAE;MAAAD,QAAA,EAAEA;IAAQ,CAAO,CAAC;EAClE;EAEA,oBACEf,IAAA,CAACH,gBAAgB;IAACO,cAAc,EAAEA,cAAe;IAACY,KAAK,EAAEA,KAAM;IAAAD,QAAA,EAC5DA;EAAQ,CACO,CAAC;AAEvB;AAEAD,sBAAsB,CAACV,cAAc,GAAGA,cAAc;AAEtD,MAAMe,MAAM,GAAG1B,UAAU,CAAC4B,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}