{"version": 3, "file": "index.js", "names": ["AllowBackup", "data", "_interopRequireWildcard", "require", "Object", "defineProperty", "exports", "enumerable", "get", "BuildProperties", "Colors", "EasBuild", "GoogleMapsApiKey", "GoogleServices", "IntentFilters", "Manifest", "Name", "Orientation", "Package", "Paths", "Permissions", "PrimaryColor", "Properties", "Resources", "Scheme", "StatusBar", "Strings", "Styles", "Updates", "Version", "WindowSoftInputMode", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "n", "__proto__", "a", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set"], "sources": ["../../src/android/index.ts"], "sourcesContent": ["import * as AllowBackup from './AllowBackup';\nimport * as BuildProperties from './BuildProperties';\nimport * as Colors from './Colors';\nimport * as EasBuild from './EasBuild';\nimport * as GoogleMapsApiKey from './GoogleMapsApiKey';\nimport * as GoogleServices from './GoogleServices';\nimport * as IntentFilters from './IntentFilters';\nimport * as Manifest from './Manifest';\nimport * as Name from './Name';\nimport * as Orientation from './Orientation';\nimport * as Package from './Package';\nimport * as Paths from './Paths';\nimport * as Permissions from './Permissions';\nimport * as PrimaryColor from './PrimaryColor';\nimport * as Properties from './Properties';\nimport * as Resources from './Resources';\nimport * as Scheme from './Scheme';\nimport * as StatusBar from './StatusBar';\nimport * as Strings from './Strings';\nimport * as Styles from './Styles';\nimport * as Updates from './Updates';\nimport * as Version from './Version';\nimport * as WindowSoftInputMode from './WindowSoftInputMode';\n\nexport { Manifest, Colors, Paths, Permissions, Properties, Resources, Scheme, Strings, Styles };\n\nexport {\n  AllowBackup,\n  BuildProperties,\n  EasBuild,\n  GoogleMapsApiKey,\n  GoogleServices,\n  IntentFilters,\n  Name,\n  Orientation,\n  Package,\n  PrimaryColor,\n  StatusBar,\n  Updates,\n  Version,\n  WindowSoftInputMode,\n};\n"], "mappings": ";;;;;;AAAA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAH,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAR,WAAA;EAAA;AAAA;AAC7C,SAAAS,gBAAA;EAAA,MAAAR,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAM,eAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAC,eAAA;EAAA;AAAA;AACrD,SAAAC,OAAA;EAAA,MAAAT,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAO,MAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAE,MAAA;EAAA;AAAA;AACnC,SAAAC,SAAA;EAAA,MAAAV,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAQ,QAAA,YAAAA,CAAA;IAAA,OAAAV,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAG,QAAA;EAAA;AAAA;AACvC,SAAAC,iBAAA;EAAA,MAAAX,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAS,gBAAA,YAAAA,CAAA;IAAA,OAAAX,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAI,gBAAA;EAAA;AAAA;AACvD,SAAAC,eAAA;EAAA,MAAAZ,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAU,cAAA,YAAAA,CAAA;IAAA,OAAAZ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAK,cAAA;EAAA;AAAA;AACnD,SAAAC,cAAA;EAAA,MAAAb,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAW,aAAA,YAAAA,CAAA;IAAA,OAAAb,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiDG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAM,aAAA;EAAA;AAAA;AACjD,SAAAC,SAAA;EAAA,MAAAd,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAY,QAAA,YAAAA,CAAA;IAAA,OAAAd,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAO,QAAA;EAAA;AAAA;AACvC,SAAAC,KAAA;EAAA,MAAAf,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAa,IAAA,YAAAA,CAAA;IAAA,OAAAf,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+BG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAQ,IAAA;EAAA;AAAA;AAC/B,SAAAC,YAAA;EAAA,MAAAhB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAc,WAAA,YAAAA,CAAA;IAAA,OAAAhB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAS,WAAA;EAAA;AAAA;AAC7C,SAAAC,QAAA;EAAA,MAAAjB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAe,OAAA,YAAAA,CAAA;IAAA,OAAAjB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAU,OAAA;EAAA;AAAA;AACrC,SAAAC,MAAA;EAAA,MAAAlB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAgB,KAAA,YAAAA,CAAA;IAAA,OAAAlB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAiCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAW,KAAA;EAAA;AAAA;AACjC,SAAAC,YAAA;EAAA,MAAAnB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAiB,WAAA,YAAAA,CAAA;IAAA,OAAAnB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAY,WAAA;EAAA;AAAA;AAC7C,SAAAC,aAAA;EAAA,MAAApB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAkB,YAAA,YAAAA,CAAA;IAAA,OAAApB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA+CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAa,YAAA;EAAA;AAAA;AAC/C,SAAAC,WAAA;EAAA,MAAArB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAmB,UAAA,YAAAA,CAAA;IAAA,OAAArB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA2CG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAc,UAAA;EAAA;AAAA;AAC3C,SAAAC,UAAA;EAAA,MAAAtB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAoB,SAAA,YAAAA,CAAA;IAAA,OAAAtB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAyCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAe,SAAA;EAAA;AAAA;AACzC,SAAAC,OAAA;EAAA,MAAAvB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAqB,MAAA,YAAAA,CAAA;IAAA,OAAAvB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAgB,MAAA;EAAA;AAAA;AACnC,SAAAC,UAAA;EAAA,MAAAxB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAsB,SAAA,YAAAA,CAAA;IAAA,OAAAxB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAyCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAiB,SAAA;EAAA;AAAA;AACzC,SAAAC,QAAA;EAAA,MAAAzB,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAuB,OAAA,YAAAA,CAAA;IAAA,OAAAzB,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAkB,OAAA;EAAA;AAAA;AACrC,SAAAC,OAAA;EAAA,MAAA1B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAwB,MAAA,YAAAA,CAAA;IAAA,OAAA1B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAmCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAmB,MAAA;EAAA;AAAA;AACnC,SAAAC,QAAA;EAAA,MAAA3B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAAyB,OAAA,YAAAA,CAAA;IAAA,OAAA3B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAoB,OAAA;EAAA;AAAA;AACrC,SAAAC,QAAA;EAAA,MAAA5B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA0B,OAAA,YAAAA,CAAA;IAAA,OAAA5B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAqCG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAqB,OAAA;EAAA;AAAA;AACrC,SAAAC,oBAAA;EAAA,MAAA7B,IAAA,GAAAC,uBAAA,CAAAC,OAAA;EAAA2B,mBAAA,YAAAA,CAAA;IAAA,OAAA7B,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6DG,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAsB,mBAAA;EAAA;AAAA;AAAA,SAAAC,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAA9B,wBAAA8B,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAA3B,GAAA,CAAAwB,CAAA,OAAAO,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAArC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAsC,wBAAA,WAAAC,CAAA,IAAAX,CAAA,oBAAAW,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAW,CAAA,SAAAG,CAAA,GAAAL,CAAA,GAAArC,MAAA,CAAAsC,wBAAA,CAAAV,CAAA,EAAAW,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAtC,GAAA,IAAAsC,CAAA,CAAAC,GAAA,IAAA3C,MAAA,CAAAC,cAAA,CAAAkC,CAAA,EAAAI,CAAA,EAAAG,CAAA,IAAAP,CAAA,CAAAI,CAAA,IAAAX,CAAA,CAAAW,CAAA,YAAAJ,CAAA,CAAAF,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAY,GAAA,CAAAf,CAAA,EAAAO,CAAA,GAAAA,CAAA", "ignoreList": []}