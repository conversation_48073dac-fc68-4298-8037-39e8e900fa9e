{"version": 3, "file": "withIosSplashXcodeProject.js", "names": ["_configPlugins", "data", "require", "_path", "_interopRequireDefault", "_withIosSplashScreenStoryboard", "e", "__esModule", "default", "debug", "withIosSplashXcodeProject", "config", "withXcodeProject", "modResults", "setSplashStoryboardAsync", "projectName", "modRequest", "project", "exports", "storyboardFilePath", "path", "join", "STORYBOARD_FILE_PATH", "hasFile", "IOSConfig", "XcodeUtils", "addResourceFileToGroup", "filepath", "groupName"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashXcodeProject.ts"], "sourcesContent": ["import { ConfigPlugin, IOSConfig, withXcodeProject, type XcodeProject } from '@expo/config-plugins';\nimport path from 'path';\n\nimport { STORYBOARD_FILE_PATH } from './withIosSplashScreenStoryboard';\n\nconst debug = require('debug')(\n  'expo:prebuild-config:expo-splash-screen:ios:xcodeproj'\n) as typeof console.log;\n\nexport const withIosSplashXcodeProject: ConfigPlugin = (config) => {\n  return withXcodeProject(config, async (config) => {\n    config.modResults = await setSplashStoryboardAsync({\n      projectName: config.modRequest.projectName!,\n      project: config.modResults,\n    });\n    return config;\n  });\n};\n\n/**\n * Modifies `.pbxproj` by:\n * - adding reference for `.storyboard` file\n */\nexport async function setSplashStoryboardAsync({\n  projectName,\n  project,\n}: {\n  projectName: string;\n  project: XcodeProject;\n}): Promise<XcodeProject> {\n  // Check if `${projectName}/SplashScreen.storyboard` already exists\n  // Path relative to `ios` directory\n  const storyboardFilePath = path.join(projectName, STORYBOARD_FILE_PATH);\n  if (!project.hasFile(storyboardFilePath)) {\n    debug(`Adding ${storyboardFilePath} to Xcode project`);\n    IOSConfig.XcodeUtils.addResourceFileToGroup({\n      filepath: storyboardFilePath,\n      groupName: projectName,\n      project,\n    });\n  }\n\n  return project;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,MAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,KAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,+BAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,8BAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuE,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEvE,MAAMG,KAAK,GAAGP,OAAO,CAAC,OAAO,CAAC,CAC5B,uDACF,CAAuB;AAEhB,MAAMQ,yBAAuC,GAAIC,MAAM,IAAK;EACjE,OAAO,IAAAC,iCAAgB,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IAChDA,MAAM,CAACE,UAAU,GAAG,MAAMC,wBAAwB,CAAC;MACjDC,WAAW,EAAEJ,MAAM,CAACK,UAAU,CAACD,WAAY;MAC3CE,OAAO,EAAEN,MAAM,CAACE;IAClB,CAAC,CAAC;IACF,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AAHAO,OAAA,CAAAR,yBAAA,GAAAA,yBAAA;AAIO,eAAeI,wBAAwBA,CAAC;EAC7CC,WAAW;EACXE;AAIF,CAAC,EAAyB;EACxB;EACA;EACA,MAAME,kBAAkB,GAAGC,eAAI,CAACC,IAAI,CAACN,WAAW,EAAEO,qDAAoB,CAAC;EACvE,IAAI,CAACL,OAAO,CAACM,OAAO,CAACJ,kBAAkB,CAAC,EAAE;IACxCV,KAAK,CAAC,UAAUU,kBAAkB,mBAAmB,CAAC;IACtDK,0BAAS,CAACC,UAAU,CAACC,sBAAsB,CAAC;MAC1CC,QAAQ,EAAER,kBAAkB;MAC5BS,SAAS,EAAEb,WAAW;MACtBE;IACF,CAAC,CAAC;EACJ;EAEA,OAAOA,OAAO;AAChB", "ignoreList": []}