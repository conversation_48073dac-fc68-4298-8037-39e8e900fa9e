{"version": 3, "file": "iosResolver.js", "sourceRoot": "", "sources": ["../../src/reactNativeConfig/iosResolver.ts"], "names": [], "mappings": ";;;;;AASA,kFA2BC;AApCD,2DAA6B;AAC7B,+BAA4B;AAC5B,gDAAwB;AAOjB,KAAK,UAAU,mCAAmC,CACvD,WAAmB,EACnB,iBAA2E;IAE3E,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;QAC/B,qCAAqC;QACrC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,IAAA,WAAI,EAAC,WAAW,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC;IAC/D,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,kBAAkB,GAAG,cAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,UAAU,CAAC;IACnE,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC;QACvD,CAAC,CAAC,kBAAkB;QACpB,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAExD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IAElG,OAAO;QACL,WAAW;QACX,OAAO,EAAE,WAAW,CAAC,OAAO;QAC5B,cAAc,EAAE,iBAAiB,EAAE,cAAc,IAAI,EAAE;QACvD,YAAY,EAAE,iBAAiB,EAAE,YAAY,IAAI,EAAE;KACpD,CAAC;AACJ,CAAC", "sourcesContent": ["import fs from 'fs/promises';\nimport { glob } from 'glob';\nimport path from 'path';\n\nimport type {\n  RNConfigDependencyIos,\n  RNConfigReactNativePlatformsConfigIos,\n} from './reactNativeConfig.types';\n\nexport async function resolveDependencyConfigImplIosAsync(\n  packageRoot: string,\n  reactNativeConfig: RNConfigReactNativePlatformsConfigIos | null | undefined\n): Promise<RNConfigDependencyIos | null> {\n  if (reactNativeConfig === null) {\n    // Skip autolinking for this package.\n    return null;\n  }\n\n  const podspecs = await glob('*.podspec', { cwd: packageRoot });\n  if (!podspecs?.length) {\n    return null;\n  }\n  const mainPackagePodspec = path.basename(packageRoot) + '.podspec';\n  const podspecFile = podspecs.includes(mainPackagePodspec)\n    ? mainPackagePodspec\n    : podspecs.sort((a, b) => a.localeCompare(b))[0];\n  const podspecPath = path.join(packageRoot, podspecFile);\n\n  const packageJson = JSON.parse(await fs.readFile(path.join(packageRoot, 'package.json'), 'utf8'));\n\n  return {\n    podspecPath,\n    version: packageJson.version,\n    configurations: reactNativeConfig?.configurations || [],\n    scriptPhases: reactNativeConfig?.scriptPhases || [],\n  };\n}\n"]}