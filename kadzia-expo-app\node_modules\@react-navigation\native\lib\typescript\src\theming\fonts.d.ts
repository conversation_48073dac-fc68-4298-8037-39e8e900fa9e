export declare const fonts: {
    readonly regular: {
        readonly fontFamily: "system-ui, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"";
        readonly fontWeight: "400";
    };
    readonly medium: {
        readonly fontFamily: "system-ui, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"";
        readonly fontWeight: "500";
    };
    readonly bold: {
        readonly fontFamily: "system-ui, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"";
        readonly fontWeight: "600";
    };
    readonly heavy: {
        readonly fontFamily: "system-ui, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\"";
        readonly fontWeight: "700";
    };
} | {
    readonly regular: {
        readonly fontFamily: "System";
        readonly fontWeight: "400";
    };
    readonly medium: {
        readonly fontFamily: "System";
        readonly fontWeight: "500";
    };
    readonly bold: {
        readonly fontFamily: "System";
        readonly fontWeight: "600";
    };
    readonly heavy: {
        readonly fontFamily: "System";
        readonly fontWeight: "700";
    };
} | {
    readonly regular: {
        readonly fontFamily: "sans-serif";
        readonly fontWeight: "normal";
    };
    readonly medium: {
        readonly fontFamily: "sans-serif-medium";
        readonly fontWeight: "normal";
    };
    readonly bold: {
        readonly fontFamily: "sans-serif";
        readonly fontWeight: "600";
    };
    readonly heavy: {
        readonly fontFamily: "sans-serif";
        readonly fontWeight: "700";
    };
};
//# sourceMappingURL=fonts.d.ts.map