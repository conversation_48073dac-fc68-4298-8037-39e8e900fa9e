import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for your database tables
export interface Profile {
  id: string
  username: string
  full_name: string
  avatar_url?: string
  bio?: string
  website?: string
  created_at: string
  updated_at: string
}

export interface Post {
  id: string
  user_id: string
  content: string
  image_url?: string
  video_url?: string
  likes_count: number
  comments_count: number
  is_premium: boolean
  price?: number
  created_at: string
  updated_at: string
}

export interface Comment {
  id: string
  post_id: string
  user_id: string
  content: string
  created_at: string
}

export interface Like {
  id: string
  post_id: string
  user_id: string
  created_at: string
}

export interface Follow {
  id: string
  follower_id: string
  following_id: string
  created_at: string
}

export interface Subscription {
  id: string
  subscriber_id: string
  creator_id: string
  tier: 'basic' | 'premium' | 'vip'
  price: number
  status: 'active' | 'cancelled' | 'expired'
  created_at: string
  expires_at: string
}
