{"version": 3, "file": "history.js", "names": ["getHistoryItem", "config", "name", "_internal", "pluginHistory", "addHistoryItem", "item", "version"], "sources": ["../../src/utils/history.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { ModPlatform } from '../Plugin.types';\n\nexport type PluginHistoryItem = {\n  name: string;\n  version: string;\n  platform?: ModPlatform;\n};\n\nexport function getHistoryItem(\n  config: Pick<ExpoConfig, '_internal'>,\n  name: string\n): PluginHistoryItem | null {\n  return config._internal?.pluginHistory?.[name] ?? null;\n}\n\nexport function addHistoryItem(\n  config: ExpoConfig,\n  item: Omit<PluginHistoryItem, 'version'> & { version?: string }\n): ExpoConfig {\n  if (!config._internal) {\n    config._internal = {};\n  }\n  if (!config._internal.pluginHistory) {\n    config._internal.pluginHistory = {};\n  }\n\n  if (!item.version) {\n    item.version = 'UNVERSIONED';\n  }\n\n  config._internal.pluginHistory[item.name] = item;\n\n  return config;\n}\n"], "mappings": ";;;;;;;AAUO,SAASA,cAAcA,CAC5BC,MAAqC,EACrCC,IAAY,EACc;EAC1B,OAAOD,MAAM,CAACE,SAAS,EAAEC,aAAa,GAAGF,IAAI,CAAC,IAAI,IAAI;AACxD;AAEO,SAASG,cAAcA,CAC5BJ,MAAkB,EAClBK,IAA+D,EACnD;EACZ,IAAI,CAACL,MAAM,CAACE,SAAS,EAAE;IACrBF,MAAM,CAACE,SAAS,GAAG,CAAC,CAAC;EACvB;EACA,IAAI,CAACF,MAAM,CAACE,SAAS,CAACC,aAAa,EAAE;IACnCH,MAAM,CAACE,SAAS,CAACC,aAAa,GAAG,CAAC,CAAC;EACrC;EAEA,IAAI,CAACE,IAAI,CAACC,OAAO,EAAE;IACjBD,IAAI,CAACC,OAAO,GAAG,aAAa;EAC9B;EAEAN,MAAM,CAACE,SAAS,CAACC,aAAa,CAACE,IAAI,CAACJ,IAAI,CAAC,GAAGI,IAAI;EAEhD,OAAOL,MAAM;AACf", "ignoreList": []}