{"version": 3, "file": "withIosSplashAssets.js", "names": ["_configPlugins", "data", "require", "_imageUtils", "_debug", "_interopRequireDefault", "_fs", "_path", "_AssetContents", "e", "__esModule", "default", "debug", "Debug", "IMAGE_CACHE_NAME", "IMAGESET_PATH", "PNG_FILENAME", "DARK_PNG_FILENAME", "TABLET_PNG_FILENAME", "DARK_TABLET_PNG_FILENAME", "withIosSplashAssets", "config", "splash", "withDangerousMod", "iosNamedProjectRoot", "IOSConfig", "Paths", "getSourceRoot", "modRequest", "projectRoot", "configureImageAssets", "image", "darkImage", "dark", "tabletImage", "darkTabletImage", "imageWidth", "enableFullScreenImage", "enableFullScreenImage_legacy", "exports", "imageSetPath", "path", "resolve", "fs", "promises", "rm", "force", "recursive", "writeContentsJsonFileAsync", "assetPath", "copyImageFiles", "generateImagesAssetsAsync", "generateImageAsset", "item", "fileName", "ratio", "suffix", "map", "size", "source", "generateImageAsync", "cacheType", "src", "width", "undefined", "height", "writeFile", "anyItem", "darkItem", "tabletItem", "darkTabletItem", "items", "filter", "Promise", "all", "dark<PERSON><PERSON><PERSON>ces", "appearance", "value", "buildContentsJsonImages", "createContentsJsonItem", "idiom", "filename", "scale", "appearances", "Boolean", "images", "writeContentsJsonAsync"], "sources": ["../../../../src/plugins/unversioned/expo-splash-screen/withIosSplashAssets.ts"], "sourcesContent": ["import { ConfigPlugin, IOSConfig, withDangerousMod } from '@expo/config-plugins';\nimport { generateImageAsync } from '@expo/image-utils';\nimport Debug from 'debug';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { IOSSplashConfig } from './getIosSplashConfig';\nimport {\n  ContentsJsonImage,\n  ContentsJsonAppearance,\n  createContentsJsonItem,\n  writeContentsJsonAsync,\n} from '../../icons/AssetContents';\n\nconst debug = Debug('expo:prebuild-config:expo-splash-screen:ios:assets');\n\nconst IMAGE_CACHE_NAME = 'splash-ios';\nconst IMAGESET_PATH = 'Images.xcassets/SplashScreenLogo.imageset';\nconst PNG_FILENAME = 'image';\nconst DARK_PNG_FILENAME = 'dark_image';\nconst TABLET_PNG_FILENAME = 'tablet_image';\nconst DARK_TABLET_PNG_FILENAME = 'dark_tablet_image';\n\nexport const withIosSplashAssets: ConfigPlugin<IOSSplashConfig> = (config, splash) => {\n  if (!splash) {\n    return config;\n  }\n  return withDangerousMod(config, [\n    'ios',\n    async (config) => {\n      const iosNamedProjectRoot = IOSConfig.Paths.getSourceRoot(config.modRequest.projectRoot);\n\n      await configureImageAssets({\n        projectRoot: config.modRequest.projectRoot,\n        iosNamedProjectRoot,\n        image: splash.image,\n        darkImage: splash.dark?.image,\n        tabletImage: splash.tabletImage,\n        darkTabletImage: splash.dark?.tabletImage,\n        imageWidth: splash.imageWidth ?? 100,\n        enableFullScreenImage: splash.enableFullScreenImage_legacy,\n      });\n\n      return config;\n    },\n  ]);\n};\n\n/**\n * Creates imageset containing image for Splash/Launch Screen.\n */\nasync function configureImageAssets({\n  projectRoot,\n  iosNamedProjectRoot,\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n  imageWidth,\n  enableFullScreenImage,\n}: {\n  projectRoot: string;\n  iosNamedProjectRoot: string;\n  image?: string | null;\n  darkImage?: string | null;\n  tabletImage?: string;\n  darkTabletImage?: string | null;\n  imageWidth: number;\n  enableFullScreenImage?: boolean;\n}) {\n  const imageSetPath = path.resolve(iosNamedProjectRoot, IMAGESET_PATH);\n  // ensure old SplashScreen imageSet is removed\n  await fs.promises.rm(imageSetPath, { force: true, recursive: true });\n\n  if (!image) {\n    return;\n  }\n\n  await writeContentsJsonFileAsync({\n    assetPath: imageSetPath,\n    image: PNG_FILENAME,\n    darkImage: darkImage ? DARK_PNG_FILENAME : null,\n    tabletImage: tabletImage ? TABLET_PNG_FILENAME : null,\n    darkTabletImage: darkTabletImage ? DARK_TABLET_PNG_FILENAME : null,\n  });\n\n  await copyImageFiles({\n    projectRoot,\n    iosNamedProjectRoot,\n    image,\n    darkImage,\n    tabletImage,\n    darkTabletImage,\n    imageWidth,\n    enableFullScreenImage,\n  });\n}\n\nasync function copyImageFiles({\n  projectRoot,\n  iosNamedProjectRoot,\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n  imageWidth,\n  enableFullScreenImage,\n}: {\n  projectRoot: string;\n  iosNamedProjectRoot: string;\n  image: string;\n  darkImage?: string | null;\n  tabletImage?: string | null;\n  darkTabletImage?: string | null;\n  imageWidth: number;\n  enableFullScreenImage?: boolean;\n}) {\n  await generateImagesAssetsAsync({\n    async generateImageAsset(item, fileName) {\n      [\n        { ratio: 1, suffix: '' },\n        { ratio: 2, suffix: '@2x' },\n        { ratio: 3, suffix: '@3x' },\n      ].map(async ({ ratio, suffix }) => {\n        const size = imageWidth * ratio;\n        // Using this method will cache the images in `.expo` based on the properties used to generate them.\n        // this method also supports remote URLs and using the global sharp instance.\n        const { source } = await generateImageAsync({ projectRoot, cacheType: IMAGE_CACHE_NAME }, {\n          src: item,\n          width: enableFullScreenImage ? undefined : size,\n          height: enableFullScreenImage ? undefined : size,\n        } as any);\n        // Write image buffer to the file system.\n        // const assetPath = join(iosNamedProjectRoot, IMAGESET_PATH, filename);\n        await fs.promises.writeFile(\n          path.resolve(iosNamedProjectRoot, IMAGESET_PATH, `${fileName}${suffix}.png`),\n          source\n        );\n      });\n    },\n    anyItem: image,\n    darkItem: darkImage,\n    tabletItem: tabletImage,\n    darkTabletItem: darkTabletImage,\n  });\n}\n\nasync function generateImagesAssetsAsync({\n  generateImageAsset,\n  anyItem,\n  darkItem,\n  tabletItem,\n  darkTabletItem,\n}: {\n  generateImageAsset: (item: string, fileName: string) => Promise<void>;\n  anyItem: string;\n  darkItem?: string | null;\n  tabletItem?: string | null;\n  darkTabletItem?: string | null;\n}) {\n  const items = [\n    [anyItem, PNG_FILENAME],\n    [darkItem, DARK_PNG_FILENAME],\n    [tabletItem, TABLET_PNG_FILENAME],\n    [darkTabletItem, DARK_TABLET_PNG_FILENAME],\n  ].filter(([item]) => !!item) as unknown as [string, string];\n\n  await Promise.all(items.map(([item, fileName]) => generateImageAsset(item, fileName)));\n}\nconst darkAppearances: ContentsJsonAppearance[] = [\n  {\n    appearance: 'luminosity',\n    value: 'dark',\n  } as ContentsJsonAppearance,\n];\n\nexport function buildContentsJsonImages({\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n}: {\n  image: string;\n  tabletImage: string | null;\n  darkImage: string | null;\n  darkTabletImage: string | null;\n}): ContentsJsonImage[] {\n  return [\n    // Phone light\n    createContentsJsonItem({\n      idiom: 'universal',\n      filename: `${image}.png`,\n      scale: '1x',\n    }),\n    createContentsJsonItem({\n      idiom: 'universal',\n      filename: `${image}@2x.png`,\n      scale: '2x',\n    }),\n    createContentsJsonItem({\n      idiom: 'universal',\n      filename: `${image}@3x.png`,\n      scale: '3x',\n    }),\n    // Phone dark\n    darkImage &&\n      createContentsJsonItem({\n        idiom: 'universal',\n        appearances: darkAppearances,\n        scale: '1x',\n        filename: `${darkImage}.png`,\n      }),\n    darkImage &&\n      createContentsJsonItem({\n        idiom: 'universal',\n        appearances: darkAppearances,\n        scale: '2x',\n        filename: `${darkImage}@2x.png`,\n      }),\n    darkImage &&\n      createContentsJsonItem({\n        idiom: 'universal',\n        appearances: darkAppearances,\n        scale: '3x',\n        filename: `${darkImage}@3x.png`,\n      }),\n    // Tablet light\n    tabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        filename: `${tabletImage}.png`,\n        scale: '1x',\n      }),\n    tabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        scale: '2x',\n        filename: `${tabletImage}@2x.png`,\n      }),\n    // Phone dark\n    darkTabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        appearances: darkAppearances,\n        filename: `${darkTabletImage}.png`,\n        scale: '1x',\n      }),\n    darkTabletImage &&\n      createContentsJsonItem({\n        idiom: 'ipad',\n        appearances: darkAppearances,\n        filename: `${darkTabletImage}@2x.png`,\n        scale: '2x',\n      }),\n  ].filter(Boolean) as ContentsJsonImage[];\n}\n\nasync function writeContentsJsonFileAsync({\n  assetPath,\n  image,\n  darkImage,\n  tabletImage,\n  darkTabletImage,\n}: {\n  assetPath: string;\n  image: string;\n  darkImage: string | null;\n  tabletImage: string | null;\n  darkTabletImage: string | null;\n}) {\n  const images = buildContentsJsonImages({ image, darkImage, tabletImage, darkTabletImage });\n\n  debug(`create contents.json:`, assetPath);\n  debug(`use images:`, images);\n  await writeContentsJsonAsync(assetPath, { images });\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,YAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,OAAA;EAAA,MAAAH,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAE,MAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,IAAA;EAAA,MAAAL,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAI,GAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,MAAA;EAAA,MAAAN,IAAA,GAAAI,sBAAA,CAAAH,OAAA;EAAAK,KAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,SAAAO,eAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,cAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKmC,SAAAI,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEnC,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,oDAAoD,CAAC;AAEzE,MAAMC,gBAAgB,GAAG,YAAY;AACrC,MAAMC,aAAa,GAAG,2CAA2C;AACjE,MAAMC,YAAY,GAAG,OAAO;AAC5B,MAAMC,iBAAiB,GAAG,YAAY;AACtC,MAAMC,mBAAmB,GAAG,cAAc;AAC1C,MAAMC,wBAAwB,GAAG,mBAAmB;AAE7C,MAAMC,mBAAkD,GAAGA,CAACC,MAAM,EAAEC,MAAM,KAAK;EACpF,IAAI,CAACA,MAAM,EAAE;IACX,OAAOD,MAAM;EACf;EACA,OAAO,IAAAE,iCAAgB,EAACF,MAAM,EAAE,CAC9B,KAAK,EACL,MAAOA,MAAM,IAAK;IAChB,MAAMG,mBAAmB,GAAGC,0BAAS,CAACC,KAAK,CAACC,aAAa,CAACN,MAAM,CAACO,UAAU,CAACC,WAAW,CAAC;IAExF,MAAMC,oBAAoB,CAAC;MACzBD,WAAW,EAAER,MAAM,CAACO,UAAU,CAACC,WAAW;MAC1CL,mBAAmB;MACnBO,KAAK,EAAET,MAAM,CAACS,KAAK;MACnBC,SAAS,EAAEV,MAAM,CAACW,IAAI,EAAEF,KAAK;MAC7BG,WAAW,EAAEZ,MAAM,CAACY,WAAW;MAC/BC,eAAe,EAAEb,MAAM,CAACW,IAAI,EAAEC,WAAW;MACzCE,UAAU,EAAEd,MAAM,CAACc,UAAU,IAAI,GAAG;MACpCC,qBAAqB,EAAEf,MAAM,CAACgB;IAChC,CAAC,CAAC;IAEF,OAAOjB,MAAM;EACf,CAAC,CACF,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AAFAkB,OAAA,CAAAnB,mBAAA,GAAAA,mBAAA;AAGA,eAAeU,oBAAoBA,CAAC;EAClCD,WAAW;EACXL,mBAAmB;EACnBO,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC,eAAe;EACfC,UAAU;EACVC;AAUF,CAAC,EAAE;EACD,MAAMG,YAAY,GAAGC,eAAI,CAACC,OAAO,CAAClB,mBAAmB,EAAET,aAAa,CAAC;EACrE;EACA,MAAM4B,aAAE,CAACC,QAAQ,CAACC,EAAE,CAACL,YAAY,EAAE;IAAEM,KAAK,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAK,CAAC,CAAC;EAEpE,IAAI,CAAChB,KAAK,EAAE;IACV;EACF;EAEA,MAAMiB,0BAA0B,CAAC;IAC/BC,SAAS,EAAET,YAAY;IACvBT,KAAK,EAAEf,YAAY;IACnBgB,SAAS,EAAEA,SAAS,GAAGf,iBAAiB,GAAG,IAAI;IAC/CiB,WAAW,EAAEA,WAAW,GAAGhB,mBAAmB,GAAG,IAAI;IACrDiB,eAAe,EAAEA,eAAe,GAAGhB,wBAAwB,GAAG;EAChE,CAAC,CAAC;EAEF,MAAM+B,cAAc,CAAC;IACnBrB,WAAW;IACXL,mBAAmB;IACnBO,KAAK;IACLC,SAAS;IACTE,WAAW;IACXC,eAAe;IACfC,UAAU;IACVC;EACF,CAAC,CAAC;AACJ;AAEA,eAAea,cAAcA,CAAC;EAC5BrB,WAAW;EACXL,mBAAmB;EACnBO,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC,eAAe;EACfC,UAAU;EACVC;AAUF,CAAC,EAAE;EACD,MAAMc,yBAAyB,CAAC;IAC9B,MAAMC,kBAAkBA,CAACC,IAAI,EAAEC,QAAQ,EAAE;MACvC,CACE;QAAEC,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAG,CAAC,EACxB;QAAED,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAM,CAAC,EAC3B;QAAED,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAM,CAAC,CAC5B,CAACC,GAAG,CAAC,OAAO;QAAEF,KAAK;QAAEC;MAAO,CAAC,KAAK;QACjC,MAAME,IAAI,GAAGtB,UAAU,GAAGmB,KAAK;QAC/B;QACA;QACA,MAAM;UAAEI;QAAO,CAAC,GAAG,MAAM,IAAAC,gCAAkB,EAAC;UAAE/B,WAAW;UAAEgC,SAAS,EAAE/C;QAAiB,CAAC,EAAE;UACxFgD,GAAG,EAAET,IAAI;UACTU,KAAK,EAAE1B,qBAAqB,GAAG2B,SAAS,GAAGN,IAAI;UAC/CO,MAAM,EAAE5B,qBAAqB,GAAG2B,SAAS,GAAGN;QAC9C,CAAQ,CAAC;QACT;QACA;QACA,MAAMf,aAAE,CAACC,QAAQ,CAACsB,SAAS,CACzBzB,eAAI,CAACC,OAAO,CAAClB,mBAAmB,EAAET,aAAa,EAAE,GAAGuC,QAAQ,GAAGE,MAAM,MAAM,CAAC,EAC5EG,MACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IACDQ,OAAO,EAAEpC,KAAK;IACdqC,QAAQ,EAAEpC,SAAS;IACnBqC,UAAU,EAAEnC,WAAW;IACvBoC,cAAc,EAAEnC;EAClB,CAAC,CAAC;AACJ;AAEA,eAAegB,yBAAyBA,CAAC;EACvCC,kBAAkB;EAClBe,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC;AAOF,CAAC,EAAE;EACD,MAAMC,KAAK,GAAG,CACZ,CAACJ,OAAO,EAAEnD,YAAY,CAAC,EACvB,CAACoD,QAAQ,EAAEnD,iBAAiB,CAAC,EAC7B,CAACoD,UAAU,EAAEnD,mBAAmB,CAAC,EACjC,CAACoD,cAAc,EAAEnD,wBAAwB,CAAC,CAC3C,CAACqD,MAAM,CAAC,CAAC,CAACnB,IAAI,CAAC,KAAK,CAAC,CAACA,IAAI,CAAgC;EAE3D,MAAMoB,OAAO,CAACC,GAAG,CAACH,KAAK,CAACd,GAAG,CAAC,CAAC,CAACJ,IAAI,EAAEC,QAAQ,CAAC,KAAKF,kBAAkB,CAACC,IAAI,EAAEC,QAAQ,CAAC,CAAC,CAAC;AACxF;AACA,MAAMqB,eAAyC,GAAG,CAChD;EACEC,UAAU,EAAE,YAAY;EACxBC,KAAK,EAAE;AACT,CAAC,CACF;AAEM,SAASC,uBAAuBA,CAAC;EACtC/C,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC;AAMF,CAAC,EAAuB;EACtB,OAAO;EACL;EACA,IAAA4C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,GAAGlD,KAAK,MAAM;IACxBmD,KAAK,EAAE;EACT,CAAC,CAAC,EACF,IAAAH,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,GAAGlD,KAAK,SAAS;IAC3BmD,KAAK,EAAE;EACT,CAAC,CAAC,EACF,IAAAH,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,GAAGlD,KAAK,SAAS;IAC3BmD,KAAK,EAAE;EACT,CAAC,CAAC;EACF;EACAlD,SAAS,IACP,IAAA+C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBG,WAAW,EAAER,eAAe;IAC5BO,KAAK,EAAE,IAAI;IACXD,QAAQ,EAAE,GAAGjD,SAAS;EACxB,CAAC,CAAC,EACJA,SAAS,IACP,IAAA+C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBG,WAAW,EAAER,eAAe;IAC5BO,KAAK,EAAE,IAAI;IACXD,QAAQ,EAAE,GAAGjD,SAAS;EACxB,CAAC,CAAC,EACJA,SAAS,IACP,IAAA+C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,WAAW;IAClBG,WAAW,EAAER,eAAe;IAC5BO,KAAK,EAAE,IAAI;IACXD,QAAQ,EAAE,GAAGjD,SAAS;EACxB,CAAC,CAAC;EACJ;EACAE,WAAW,IACT,IAAA6C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,GAAG/C,WAAW,MAAM;IAC9BgD,KAAK,EAAE;EACT,CAAC,CAAC,EACJhD,WAAW,IACT,IAAA6C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbE,KAAK,EAAE,IAAI;IACXD,QAAQ,EAAE,GAAG/C,WAAW;EAC1B,CAAC,CAAC;EACJ;EACAC,eAAe,IACb,IAAA4C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbG,WAAW,EAAER,eAAe;IAC5BM,QAAQ,EAAE,GAAG9C,eAAe,MAAM;IAClC+C,KAAK,EAAE;EACT,CAAC,CAAC,EACJ/C,eAAe,IACb,IAAA4C,uCAAsB,EAAC;IACrBC,KAAK,EAAE,MAAM;IACbG,WAAW,EAAER,eAAe;IAC5BM,QAAQ,EAAE,GAAG9C,eAAe,SAAS;IACrC+C,KAAK,EAAE;EACT,CAAC,CAAC,CACL,CAACV,MAAM,CAACY,OAAO,CAAC;AACnB;AAEA,eAAepC,0BAA0BA,CAAC;EACxCC,SAAS;EACTlB,KAAK;EACLC,SAAS;EACTE,WAAW;EACXC;AAOF,CAAC,EAAE;EACD,MAAMkD,MAAM,GAAGP,uBAAuB,CAAC;IAAE/C,KAAK;IAAEC,SAAS;IAAEE,WAAW;IAAEC;EAAgB,CAAC,CAAC;EAE1FvB,KAAK,CAAC,uBAAuB,EAAEqC,SAAS,CAAC;EACzCrC,KAAK,CAAC,aAAa,EAAEyE,MAAM,CAAC;EAC5B,MAAM,IAAAC,uCAAsB,EAACrC,SAAS,EAAE;IAAEoC;EAAO,CAAC,CAAC;AACrD", "ignoreList": []}