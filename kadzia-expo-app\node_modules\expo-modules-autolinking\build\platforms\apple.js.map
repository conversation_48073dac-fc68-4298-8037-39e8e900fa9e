{"version": 3, "file": "apple.js", "sourceRoot": "", "sources": ["../../src/platforms/apple.ts"], "names": [], "mappings": ";;;;;AAkCA,kDASC;AAKD,gDA6BC;AAED,gFAcC;AAKD,oEAeC;AA4ID,8EASC;AAtQD,oEAA2C;AAC3C,4CAAoB;AACpB,+BAA4B;AAC5B,gDAAwB;AAExB,4CAA+C;AAU/C,MAAM,qBAAqB,GAAG,yBAAyB,CAAC;AACxD,MAAM,0BAA0B,GAAG,iBAAiB,CAAC;AAErD,MAAM,MAAM,GAAG,IAAI,CAAC;AAEpB,KAAK,UAAU,gBAAgB,CAAC,QAAyB;IACvD,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM,EAAE,iBAAiB,EAAE,CAAC;IAChE,IAAI,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,EAAE,CAAC;QACpD,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,MAAM,YAAY,GAAG,MAAM,IAAA,WAAI,EAAC,aAAa,EAAE;QAC7C,GAAG,EAAE,QAAQ,CAAC,IAAI;QAClB,MAAM,EAAE,CAAC,oBAAoB,CAAC;KAC/B,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,SAAgB,mBAAmB,CACjC,IAA4B,EAC5B,gBAAsC;IAEtC,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,MAAM,EAAE,CAAC;QAChD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IACD,+FAA+F;IAC/F,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC;AACtE,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,kBAAkB,CACtC,WAAmB,EACnB,QAAyB,EACzB,OAAsB;IAEtB,MAAM,YAAY,GAAG,MAAM,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACtD,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QAC9C,OAAO,EAAE,cAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,cAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9D,UAAU,EAAE,cAAI,CAAC,OAAO,CAAC,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;KAChE,CAAC,CAAC,CAAC;IAEJ,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,EAAE,qBAAqB,EAAE,CAAC,CAAC;IAC7F,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;IAE3D,OAAO;QACL,WAAW;QACX,IAAI;QACJ,gBAAgB;QAChB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,OAAO,EAAE,QAAQ,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE;QAC9C,sBAAsB,EAAE,QAAQ,CAAC,MAAM,EAAE,2BAA2B,EAAE,IAAI,EAAE;QAC5E,qBAAqB,EAAE,QAAQ,CAAC,MAAM,EAAE,0BAA0B,EAAE,IAAI,EAAE;QAC1E,SAAS,EAAE,QAAQ,CAAC,MAAM,EAAE,cAAc,EAAE,IAAI,KAAK;QACrD,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KACrD,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,kCAAkC,CACtD,iBAAyB;IAEzB,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,qBAAqB,CAAC,CAAC;IACtE,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACzC,IAAI,WAAW,CAAC,0BAA0B,CAAC,EAAE,CAAC;YAC5C,gGAAgG;YAChG,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAC,CAAC;YACtE,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;IACV,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,4BAA4B,CAChD,OAA8B,EAC9B,UAAkB,EAClB,eAAuB;IAEvB,MAAM,SAAS,GAAG,cAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;IACtE,MAAM,YAAY,GAAG,MAAM,sBAAsB,CAAC,eAAe,CAAC,CAAC;IACnE,MAAM,oBAAoB,GAAG,MAAM,mCAAmC,CACpE,OAAO,EACP,SAAS,EACT,YAAY,CACb,CAAC;IACF,MAAM,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC5C,MAAM,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACzD,MAAM,YAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,oBAAoB,EAAE,MAAM,CAAC,CAAC;AACxE,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mCAAmC,CAChD,OAA8B,EAC9B,SAAiB,EACjB,YAAuC;IAEvC,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAC/B,CAAC,MAAM,EAAE,EAAE,CACT,MAAM,CAAC,OAAO,CAAC,MAAM;QACrB,MAAM,CAAC,sBAAsB,CAAC,MAAM;QACpC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CACtC,CAAC;IAEF,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACzE,MAAM,gBAAgB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAEzE,MAAM,YAAY,GAAI,EAAe;SAClC,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SACnE,MAAM,CAAC,OAAO,CAAC,CAAC;IAEnB,MAAM,qBAAqB,GAAI,EAAe;SAC3C,MAAM,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SACpE,MAAM,CAAC,OAAO,CAAC,CAAC;IAEnB,MAAM,iBAAiB,GAAI,EAAe;SACvC,MAAM,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC1D,MAAM,CAAC,OAAO,CAAC,CAAC;IAEnB,MAAM,0BAA0B,GAAI,EAAe;SAChD,MAAM,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;SAC3D,MAAM,CAAC,OAAO,CAAC,CAAC;IAEnB,MAAM,sBAAsB,GAAI,EAAe,CAAC,MAAM,CACpD,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAClE,CAAC;IAEF,MAAM,+BAA+B,GAAI,EAAe,CAAC,MAAM,CAC7D,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,sBAAsB,CAAC,CACnE,CAAC;IAEF,MAAM,2BAA2B,GAAG,eAAe,CAAC,MAAM,CACxD,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAClD,CAAC;IAEF,MAAM,oCAAoC,GAAG,gBAAgB,CAAC,MAAM,CAClE,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAClD,CAAC;IAEF,OAAO;;;;;;;;EAQP,wBAAwB,CAAC,YAAY,CAAC;EACtC,2BAA2B,CAAC,qBAAqB,CAAC;QAC5C,SAAS;eACF,SAAS;;EAEtB,qBAAqB,CAAC,iBAAiB,EAAE,0BAA0B,CAAC;;;;EAIpE,qBAAqB,CAAC,sBAAsB,EAAE,+BAA+B,CAAC;;;;EAI9E,6BAA6B,CAAC,2BAA2B,EAAE,oCAAoC,CAAC;;;;kDAIhD,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;;;CAG7E,CAAC;AACF,CAAC;AAED,SAAS,wBAAwB,CAAC,YAAsB;IACtD,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7E,CAAC;AAED,SAAS,2BAA2B,CAAC,YAAsB;IACzD,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QACzB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,OAAO,CACL,6BAA6B,CAC3B,CAAC,EACD,YAAY,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACpE,GAAG,IAAI,CACT,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAAC,UAAoB,EAAE,kBAA4B;IAC/E,MAAM,gBAAgB,GAAG,uBAAuB,CAAC,UAAU,CAAC,CAAC;IAC7D,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAClC,OAAO,6BAA6B,CAClC,CAAC,EACD,UAAU,uBAAuB,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,EAAE,EAC1E,UAAU,gBAAgB,EAAE,CAC7B,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,gBAAgB,EAAE,CAAC;IACzD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAAC,UAAoB;IACnD,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,SAAS,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EAC5F,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AAED,SAAS,6BAA6B,CACpC,MAA6B,EAC7B,gBAAuC;IAEvC,MAAM,aAAa,GAAG,iCAAiC,CAAC,MAAM,CAAC,CAAC;IAChE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChC,OAAO,6BAA6B,CAClC,CAAC,EACD,UAAU,iCAAiC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,EAAE,EAC9E,UAAU,aAAa,EAAE,CAC1B,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,aAAa,EAAE,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,iCAAiC,CAAC,OAA8B;IAC9E,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,qBAAqB,EAAE,CAAC;YACnD,MAAM,CAAC,IAAI,CAAC,kBAAkB,MAAM,CAAC,WAAW,eAAe,OAAO,QAAQ,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IACD,OAAO,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EAC3E,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;AACtB,CAAC;AAED,SAAS,6BAA6B,CACpC,gBAAwB,EACxB,UAAkB,EAClB,eAA8B,IAAI;IAElC,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,MAAM,CAAC,MAAM,CACrF,gBAAgB,CACjB,GAAG,UAAU,KAAK,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,MAAM,CAAC,MAAM,CACvE,gBAAgB,CACjB,GAAG,YAAY,KAAK,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;IAC/D,CAAC;IAED,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,iCAAiC,MAAM,CAAC,MAAM,CACrF,gBAAgB,CACjB,GAAG,UAAU,KAAK,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AAC7D,CAAC;AAED,KAAK,UAAU,sBAAsB,CAAC,eAAuB;IAC3D,IAAI,CAAC,CAAC,MAAM,IAAA,2BAAe,EAAC,eAAe,CAAC,CAAC,EAAE,CAAC;QAC9C,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,qBAAU,EAAC,QAAQ,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;IAChG,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5C,OAAO;QACL,SAAS,EAAE,gBAAgB,CAAC,uCAAuC,CAAC,IAAI,SAAS;KAClF,CAAC;AACJ,CAAC", "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport fs from 'fs';\nimport { glob } from 'glob';\nimport path from 'path';\n\nimport { fileExistsAsync } from '../fileUtils';\nimport type {\n  AppleCodeSignEntitlements,\n  ExtraDependencies,\n  ModuleDescriptorIos,\n  ModuleIosPodspecInfo,\n  PackageRevision,\n  SearchOptions,\n} from '../types';\n\nconst APPLE_PROPERTIES_FILE = 'Podfile.properties.json';\nconst APPLE_EXTRA_BUILD_DEPS_KEY = 'apple.extraPods';\n\nconst indent = '  ';\n\nasync function findPodspecFiles(revision: PackageRevision): Promise<string[]> {\n  const configPodspecPaths = revision.config?.applePodspecPaths();\n  if (configPodspecPaths && configPodspecPaths.length) {\n    return configPodspecPaths;\n  }\n\n  const podspecFiles = await glob('*/*.podspec', {\n    cwd: revision.path,\n    ignore: ['**/node_modules/**'],\n  });\n\n  return podspecFiles;\n}\n\nexport function getSwiftModuleNames(\n  pods: ModuleIosPodspecInfo[],\n  swiftModuleNames: string[] | undefined\n): string[] {\n  if (swiftModuleNames && swiftModuleNames.length) {\n    return swiftModuleNames;\n  }\n  // by default, non-alphanumeric characters in the pod name are replaced by _ in the module name\n  return pods.map((pod) => pod.podName.replace(/[^a-zA-Z0-9]/g, '_'));\n}\n\n/**\n * Resolves module search result with additional details required for iOS platform.\n */\nexport async function resolveModuleAsync(\n  packageName: string,\n  revision: PackageRevision,\n  options: SearchOptions\n): Promise<ModuleDescriptorIos | null> {\n  const podspecFiles = await findPodspecFiles(revision);\n  if (!podspecFiles.length) {\n    return null;\n  }\n\n  const pods = podspecFiles.map((podspecFile) => ({\n    podName: path.basename(podspecFile, path.extname(podspecFile)),\n    podspecDir: path.dirname(path.join(revision.path, podspecFile)),\n  }));\n\n  const swiftModuleNames = getSwiftModuleNames(pods, revision.config?.appleSwiftModuleNames());\n  const coreFeatures = revision.config?.coreFeatures() ?? [];\n\n  return {\n    packageName,\n    pods,\n    swiftModuleNames,\n    flags: options.flags,\n    modules: revision.config?.appleModules() ?? [],\n    appDelegateSubscribers: revision.config?.appleAppDelegateSubscribers() ?? [],\n    reactDelegateHandlers: revision.config?.appleReactDelegateHandlers() ?? [],\n    debugOnly: revision.config?.appleDebugOnly() ?? false,\n    ...(coreFeatures.length > 0 ? { coreFeatures } : {}),\n  };\n}\n\nexport async function resolveExtraBuildDependenciesAsync(\n  projectNativeRoot: string\n): Promise<ExtraDependencies | null> {\n  const propsFile = path.join(projectNativeRoot, APPLE_PROPERTIES_FILE);\n  try {\n    const contents = await fs.promises.readFile(propsFile, 'utf8');\n    const podfileJson = JSON.parse(contents);\n    if (podfileJson[APPLE_EXTRA_BUILD_DEPS_KEY]) {\n      // expo-build-properties would serialize the extraPods as JSON string, we should parse it again.\n      const extraPods = JSON.parse(podfileJson[APPLE_EXTRA_BUILD_DEPS_KEY]);\n      return extraPods;\n    }\n  } catch {}\n  return null;\n}\n\n/**\n * Generates Swift file that contains all autolinked Swift packages.\n */\nexport async function generateModulesProviderAsync(\n  modules: ModuleDescriptorIos[],\n  targetPath: string,\n  entitlementPath: string\n): Promise<void> {\n  const className = path.basename(targetPath, path.extname(targetPath));\n  const entitlements = await parseEntitlementsAsync(entitlementPath);\n  const generatedFileContent = await generatePackageListFileContentAsync(\n    modules,\n    className,\n    entitlements\n  );\n  const parentPath = path.dirname(targetPath);\n  await fs.promises.mkdir(parentPath, { recursive: true });\n  await fs.promises.writeFile(targetPath, generatedFileContent, 'utf8');\n}\n\n/**\n * Generates the string to put into the generated package list.\n */\nasync function generatePackageListFileContentAsync(\n  modules: ModuleDescriptorIos[],\n  className: string,\n  entitlements: AppleCodeSignEntitlements\n): Promise<string> {\n  const iosModules = modules.filter(\n    (module) =>\n      module.modules.length ||\n      module.appDelegateSubscribers.length ||\n      module.reactDelegateHandlers.length\n  );\n\n  const modulesToImport = iosModules.filter((module) => !module.debugOnly);\n  const debugOnlyModules = iosModules.filter((module) => module.debugOnly);\n\n  const swiftModules = ([] as string[])\n    .concat(...modulesToImport.map((module) => module.swiftModuleNames))\n    .filter(Boolean);\n\n  const debugOnlySwiftModules = ([] as string[])\n    .concat(...debugOnlyModules.map((module) => module.swiftModuleNames))\n    .filter(Boolean);\n\n  const modulesClassNames = ([] as string[])\n    .concat(...modulesToImport.map((module) => module.modules))\n    .filter(Boolean);\n\n  const debugOnlyModulesClassNames = ([] as string[])\n    .concat(...debugOnlyModules.map((module) => module.modules))\n    .filter(Boolean);\n\n  const appDelegateSubscribers = ([] as string[]).concat(\n    ...modulesToImport.map((module) => module.appDelegateSubscribers)\n  );\n\n  const debugOnlyAppDelegateSubscribers = ([] as string[]).concat(\n    ...debugOnlyModules.map((module) => module.appDelegateSubscribers)\n  );\n\n  const reactDelegateHandlerModules = modulesToImport.filter(\n    (module) => !!module.reactDelegateHandlers.length\n  );\n\n  const debugOnlyReactDelegateHandlerModules = debugOnlyModules.filter(\n    (module) => !!module.reactDelegateHandlers.length\n  );\n\n  return `/**\n * Automatically generated by expo-modules-autolinking.\n *\n * This autogenerated class provides a list of classes of native Expo modules,\n * but only these that are written in Swift and use the new API for creating Expo modules.\n */\n\nimport ExpoModulesCore\n${generateCommonImportList(swiftModules)}\n${generateDebugOnlyImportList(debugOnlySwiftModules)}\n@objc(${className})\npublic class ${className}: ModulesProvider {\n  public override func getModuleClasses() -> [AnyModule.Type] {\n${generateModuleClasses(modulesClassNames, debugOnlyModulesClassNames)}\n  }\n\n  public override func getAppDelegateSubscribers() -> [ExpoAppDelegateSubscriber.Type] {\n${generateModuleClasses(appDelegateSubscribers, debugOnlyAppDelegateSubscribers)}\n  }\n\n  public override func getReactDelegateHandlers() -> [ExpoReactDelegateHandlerTupleType] {\n${generateReactDelegateHandlers(reactDelegateHandlerModules, debugOnlyReactDelegateHandlerModules)}\n  }\n\n  public override func getAppCodeSignEntitlements() -> AppCodeSignEntitlements {\n    return AppCodeSignEntitlements.from(json: #\"${JSON.stringify(entitlements)}\"#)\n  }\n}\n`;\n}\n\nfunction generateCommonImportList(swiftModules: string[]): string {\n  return swiftModules.map((moduleName) => `import ${moduleName}`).join('\\n');\n}\n\nfunction generateDebugOnlyImportList(swiftModules: string[]): string {\n  if (!swiftModules.length) {\n    return '';\n  }\n\n  return (\n    wrapInDebugConfigurationCheck(\n      0,\n      swiftModules.map((moduleName) => `import ${moduleName}`).join('\\n')\n    ) + '\\n'\n  );\n}\n\nfunction generateModuleClasses(classNames: string[], debugOnlyClassName: string[]): string {\n  const commonClassNames = formatArrayOfClassNames(classNames);\n  if (debugOnlyClassName.length > 0) {\n    return wrapInDebugConfigurationCheck(\n      2,\n      `return ${formatArrayOfClassNames(classNames.concat(debugOnlyClassName))}`,\n      `return ${commonClassNames}`\n    );\n  } else {\n    return `${indent.repeat(2)}return ${commonClassNames}`;\n  }\n}\n\n/**\n * Formats an array of class names to Swift's array containing these classes.\n */\nfunction formatArrayOfClassNames(classNames: string[]): string {\n  return `[${classNames.map((className) => `\\n${indent.repeat(3)}${className}.self`).join(',')}\n${indent.repeat(2)}]`;\n}\n\nfunction generateReactDelegateHandlers(\n  module: ModuleDescriptorIos[],\n  debugOnlyModules: ModuleDescriptorIos[]\n): string {\n  const commonModules = formatArrayOfReactDelegateHandler(module);\n  if (debugOnlyModules.length > 0) {\n    return wrapInDebugConfigurationCheck(\n      2,\n      `return ${formatArrayOfReactDelegateHandler(module.concat(debugOnlyModules))}`,\n      `return ${commonModules}`\n    );\n  } else {\n    return `${indent.repeat(2)}return ${commonModules}`;\n  }\n}\n\n/**\n * Formats an array of modules to Swift's array containing ReactDelegateHandlers\n */\nexport function formatArrayOfReactDelegateHandler(modules: ModuleDescriptorIos[]): string {\n  const values: string[] = [];\n  for (const module of modules) {\n    for (const handler of module.reactDelegateHandlers) {\n      values.push(`(packageName: \"${module.packageName}\", handler: ${handler}.self)`);\n    }\n  }\n  return `[${values.map((value) => `\\n${indent.repeat(3)}${value}`).join(',')}\n${indent.repeat(2)}]`;\n}\n\nfunction wrapInDebugConfigurationCheck(\n  indentationLevel: number,\n  debugBlock: string,\n  releaseBlock: string | null = null\n) {\n  if (releaseBlock) {\n    return `${indent.repeat(indentationLevel)}#if EXPO_CONFIGURATION_DEBUG\\n${indent.repeat(\n      indentationLevel\n    )}${debugBlock}\\n${indent.repeat(indentationLevel)}#else\\n${indent.repeat(\n      indentationLevel\n    )}${releaseBlock}\\n${indent.repeat(indentationLevel)}#endif`;\n  }\n\n  return `${indent.repeat(indentationLevel)}#if EXPO_CONFIGURATION_DEBUG\\n${indent.repeat(\n    indentationLevel\n  )}${debugBlock}\\n${indent.repeat(indentationLevel)}#endif`;\n}\n\nasync function parseEntitlementsAsync(entitlementPath: string): Promise<AppleCodeSignEntitlements> {\n  if (!(await fileExistsAsync(entitlementPath))) {\n    return {};\n  }\n  const { stdout } = await spawnAsync('plutil', ['-convert', 'json', '-o', '-', entitlementPath]);\n  const entitlementsJson = JSON.parse(stdout);\n  return {\n    appGroups: entitlementsJson['com.apple.security.application-groups'] || undefined,\n  };\n}\n"]}