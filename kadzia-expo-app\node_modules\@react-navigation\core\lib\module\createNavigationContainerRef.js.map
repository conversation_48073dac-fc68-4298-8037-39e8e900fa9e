{"version": 3, "names": ["CommonActions", "NOT_INITIALIZED_ERROR", "createNavigationContainerRef", "methods", "Object", "keys", "listeners", "removeListener", "event", "callback", "filter", "cb", "current", "ref", "value", "entries", "for<PERSON>ach", "callbacks", "addListener", "isReady", "reduce", "acc", "name", "args", "push", "console", "error"], "sourceRoot": "../../src", "sources": ["createNavigationContainerRef.tsx"], "mappings": ";;AAAA,SAASA,aAAa,QAAQ,2BAA2B;AAQzD,OAAO,MAAMC,qBAAqB,GAChC,+QAA+Q;AAEjR,OAAO,SAASC,4BAA4BA,CAAA,EAEM;EAChD,MAAMC,OAAO,GAAG,CACd,GAAGC,MAAM,CAACC,IAAI,CAACL,aAAa,CAAC,EAC7B,aAAa,EACb,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,WAAW,EACX,WAAW,EACX,cAAc,EACd,UAAU,EACV,WAAW,EACX,iBAAiB,EACjB,mBAAmB,CACX;EAEV,MAAMM,SAAuD,GAAG,CAAC,CAAC;EAElE,MAAMC,cAAc,GAAGA,CACrBC,KAAa,EACbC,QAAkC,KAC/B;IACH,IAAIH,SAAS,CAACE,KAAK,CAAC,EAAE;MACpBF,SAAS,CAACE,KAAK,CAAC,GAAGF,SAAS,CAACE,KAAK,CAAC,CAACE,MAAM,CAAEC,EAAE,IAAKA,EAAE,KAAKF,QAAQ,CAAC;IACrE;EACF,CAAC;EAED,IAAIG,OAAiD,GAAG,IAAI;EAE5D,MAAMC,GAAiD,GAAG;IACxD,IAAID,OAAOA,CAAA,EAAG;MACZ,OAAOA,OAAO;IAChB,CAAC;IACD,IAAIA,OAAOA,CAACE,KAA+C,EAAE;MAC3DF,OAAO,GAAGE,KAAK;MAEf,IAAIA,KAAK,IAAI,IAAI,EAAE;QACjBV,MAAM,CAACW,OAAO,CAACT,SAAS,CAAC,CAACU,OAAO,CAAC,CAAC,CAACR,KAAK,EAAES,SAAS,CAAC,KAAK;UACxDA,SAAS,CAACD,OAAO,CAAEP,QAAQ,IAAK;YAC9BK,KAAK,CAACI,WAAW,CACfV,KAAK,EACLC,QACF,CAAC;UACH,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC;IACDU,OAAO,EAAEA,CAAA,KAAM;MACb,IAAIP,OAAO,IAAI,IAAI,EAAE;QACnB,OAAO,KAAK;MACd;MAEA,OAAOA,OAAO,CAACO,OAAO,CAAC,CAAC;IAC1B,CAAC;IACD,GAAGhB,OAAO,CAACiB,MAAM,CAAM,CAACC,GAAG,EAAEC,IAAI,KAAK;MACpCD,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGC,IAAW,KAAK;QAC9B,IAAIX,OAAO,IAAI,IAAI,EAAE;UACnB,QAAQU,IAAI;YACV,KAAK,aAAa;cAAE;gBAClB,MAAM,CAACd,KAAK,EAAEC,QAAQ,CAAC,GAAGc,IAAI;gBAE9BjB,SAAS,CAACE,KAAK,CAAC,GAAGF,SAAS,CAACE,KAAK,CAAC,IAAI,EAAE;gBACzCF,SAAS,CAACE,KAAK,CAAC,CAACgB,IAAI,CAACf,QAAQ,CAAC;gBAE/B,OAAO,MAAMF,cAAc,CAACC,KAAK,EAAEC,QAAQ,CAAC;cAC9C;YACA,KAAK,gBAAgB;cAAE;gBACrB,MAAM,CAACD,KAAK,EAAEC,QAAQ,CAAC,GAAGc,IAAI;gBAE9BhB,cAAc,CAACC,KAAK,EAAEC,QAAQ,CAAC;gBAC/B;cACF;YACA;cACEgB,OAAO,CAACC,KAAK,CAACzB,qBAAqB,CAAC;UACxC;QACF,CAAC,MAAM;UACL;UACA,OAAOW,OAAO,CAACU,IAAI,CAAC,CAAC,GAAGC,IAAI,CAAC;QAC/B;MACF,CAAC;MACD,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC;EACP,CAAC;EAED,OAAOR,GAAG;AACZ", "ignoreList": []}